import { Inject, Injectable, Logger } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { type ConfigType } from '@nestjs/config';
import emailConfig from '../../configs/email.config';
import * as ejs from 'ejs';
import * as path from 'path';

export interface EmailOptions {
  to: string | string[];
  subject: string;
  template: string;
  context: Record<string, any>;
  cc?: string | string[];
  bcc?: string | string[];
}
@Injectable()
export class EmailsService {
  private readonly logger = new Logger(EmailsService.name);
  private transporter: nodemailer.Transporter;

  constructor(
    @Inject(emailConfig.KEY)
    private emailConfiguration: ConfigType<typeof emailConfig>,
  ) {
    this.createTransporter();
  }

  private createTransporter() {
    this.transporter = nodemailer.createTransport({
      host: this.emailConfiguration.host,
      port: this.emailConfiguration.port,
      secure: this.emailConfiguration.secure,
      auth: {
        user: this.emailConfiguration.auth.user,
        pass: this.emailConfiguration.auth.pass,
      },
    });
    // Verify connection configuration
    this.transporter.verify((error) => {
      if (error) this.logger.error('Email configuration error:', error);
      else this.logger.log('Email service is ready to send messages');
    });
  }

  async sendEmail(options: EmailOptions): Promise<void> {
    try {
      const templatePath = path.join(
        process.cwd(),
        'src/templates/emails',
        `${options.template}.ejs`,
      );

      const html = await ejs.renderFile(templatePath, options.context);

      const mailOptions = {
        from: this.emailConfiguration.from,
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        subject: options.subject,
        html,
        cc: options.cc,
        bcc: options.bcc,
      };

      await this.transporter.sendMail(mailOptions);
    } catch (error) {
      throw new Error(`Failed to send email: ${error.message}`);
    }
  }
}
